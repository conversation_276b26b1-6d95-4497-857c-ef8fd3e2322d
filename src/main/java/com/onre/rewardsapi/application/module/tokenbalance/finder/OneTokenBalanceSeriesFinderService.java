package com.onre.rewardsapi.application.module.tokenbalance.finder;

import com.onre.rewardsapi.application.common.finder.BaseFinderService;
import com.onre.rewardsapi.application.module.tokenbalance.exception.OneTokenBalanceSeriesNotFoundException;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenBalanceSeries;
import com.onre.rewardsapi.domain.tokenbalance.repository.OneTokenBalanceSeriesRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Service
public class OneTokenBalanceSeriesFinderService extends BaseFinderService<OneTokenBalanceSeries> {

    private final OneTokenBalanceSeriesRepository repository;

    protected OneTokenBalanceSeriesFinderService(OneTokenBalanceSeriesRepository repository) {
        super(repository);
        this.repository = repository;
    }

    @Override
    protected RuntimeException createNotFoundException(String message) {
        return new OneTokenBalanceSeriesNotFoundException(message);
    }

    @Override
    protected Class<OneTokenBalanceSeries> getEntityType() {
        return OneTokenBalanceSeries.class;
    }

    @Transactional(readOnly = true)
    public Optional<OneTokenBalanceSeries> findByAddressAndTimestampAt(String address, Instant timestamp) {
        return repository.findByAddressAndTimestamp(address, timestamp);
    }


    @Transactional(readOnly = true)
    public List<OneTokenBalanceSeries> findAllByTimestampAt(Instant timestamp) {
        return repository.findAllByTimestamp(timestamp);
    }

    @Transactional(readOnly = true)
    public Page<OneTokenBalanceSeries> findAllByTimestampAt(Instant timestamp, Pageable pageable) {
        return repository.findAllByTimestamp(timestamp, pageable);
    }
}
