package com.onre.rewardsapi.application.module.tokenbalance.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class OneTokenLastBalanceNotFoundException extends ApiException {

    public OneTokenLastBalanceNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return TokenBalanceErrorType.ONE_TOKEN_LAST_TIMESTAMP_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.TOKEN_BALANCE;
    }
}
