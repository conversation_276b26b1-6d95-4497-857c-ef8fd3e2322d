package com.onre.rewardsapi.application.module.tokenbalance.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;

public class OneTokenBalanceSeriesNotFoundException extends ApiException {

    public OneTokenBalanceSeriesNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return TokenBalanceErrorType.ONE_TOKEN_BALANCE_SERIES_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.TOKEN_BALANCE;
    }
}
