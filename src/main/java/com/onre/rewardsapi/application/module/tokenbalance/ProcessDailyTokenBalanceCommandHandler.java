package com.onre.rewardsapi.application.module.tokenbalance;

import com.onre.rewardsapi.application.common.command.CommandHandler;
import com.onre.rewardsapi.application.common.properties.TokenProperties;
import com.onre.rewardsapi.application.common.util.InstantUtil;
import com.onre.rewardsapi.application.module.tokenbalance.command.ProcessDailyTokenBalanceCommand;
import com.onre.rewardsapi.application.module.tokenbalance.finder.OneTokenBalanceSeriesFinderService;
import com.onre.rewardsapi.application.module.tokenbalance.finder.OneTokenLastTimestampFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.finder.TokenTransferFinderService;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenBalanceSeries;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenBalanceSeriesCreateService;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenLastTimestamp;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenLastTimestampCreateService;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransfer;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.time.Clock;

import java.math.BigInteger;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class ProcessDailyTokenBalanceCommandHandler implements CommandHandler<Void, ProcessDailyTokenBalanceCommand> {

    private static final int GAP_PROCESSING_PAGE_SIZE = 200;

    private final OneTokenLastTimestampFinderService oneTokenLastTimestampFinderService;
    private final OneTokenBalanceSeriesCreateService oneTokenBalanceSeriesCreateService;
    private final OneTokenBalanceSeriesFinderService oneTokenBalanceSeriesFinderService;
    private final OneTokenLastTimestampCreateService oneTokenLastTimestampCreateService;
    private final TokenTransferFinderService tokenTransferFinderService;
    private final TokenProperties tokenProperties;
    private final Clock clock;

    @Lazy
    @Autowired
    private ProcessDailyTokenBalanceCommandHandler self;

    @Override
    public Class<ProcessDailyTokenBalanceCommand> getCommandType() {
        return ProcessDailyTokenBalanceCommand.class;
    }

    @Override
    public Void handle(ProcessDailyTokenBalanceCommand command) {
        Optional<OneTokenLastTimestamp> lastTimestamp = oneTokenLastTimestampFinderService.findAll().stream().findFirst();
        Instant now = Instant.now(clock);
        Instant startOfDayToProcess = lastTimestamp
                .map(oneTokenLastTimestamp -> InstantUtil.getStartOfNextDay(oneTokenLastTimestamp.getLastProcessedTimestamp()))
                .orElseGet(() -> InstantUtil.getStartOfDay(tokenProperties.one().tokenTgeTimestamp()));
        Instant endOfDayToProcess = InstantUtil.getEndOfDay(startOfDayToProcess);


        Map<String, BigInteger> dayBalanceMap = new HashMap<>();

        while (startOfDayToProcess.isBefore(InstantUtil.getStartOfDay(now))) {
            List<TokenTransfer> tokenTransfers = tokenTransferFinderService.findAllByInstantBetween(startOfDayToProcess, endOfDayToProcess);

            for (TokenTransfer tokenTransfer : tokenTransfers) {
                String fromAddress = tokenTransfer.getFromSolAddress();
                String toAddress = tokenTransfer.getToSolAddress();
                BigInteger fromPreviousBalance = oneTokenBalanceSeriesFinderService
                        .findByAddressAndTimestampAt(fromAddress, InstantUtil.getPreviousStartOfDay(startOfDayToProcess))
                        .map(OneTokenBalanceSeries::getAmount)
                        .orElse(BigInteger.ZERO);
                BigInteger toPreviousBalance = oneTokenBalanceSeriesFinderService
                        .findByAddressAndTimestampAt(toAddress, InstantUtil.getPreviousStartOfDay(startOfDayToProcess))
                        .map(OneTokenBalanceSeries::getAmount)
                        .orElse(BigInteger.ZERO);

                // Subtract transfer amount from the previous balance
                dayBalanceMap.put(fromAddress, dayBalanceMap.getOrDefault(fromAddress, fromPreviousBalance).subtract(tokenTransfer.getAmount()));
                dayBalanceMap.put(toAddress, dayBalanceMap.getOrDefault(toAddress, toPreviousBalance).add(tokenTransfer.getAmount()));
            }

            // Save the balance snapshot for the next day (balance reflects transfers from current day)
            Instant balanceTimestamp = InstantUtil.getStartOfNextDay(startOfDayToProcess);

            // Only save balances for addresses with transfers if there are any transfers
            if (!dayBalanceMap.isEmpty()) {
                for (Map.Entry<String, BigInteger> entry : dayBalanceMap.entrySet()) {
                    oneTokenBalanceSeriesCreateService.create(entry.getKey(), entry.getValue(), balanceTimestamp);
                }
            }

            // Always fill in the gaps for addresses that have no transfers, even if there are no transfers on this day
            self.fillGapsForAddressesWithoutTransfers(
                    InstantUtil.getPreviousStartOfDay(balanceTimestamp),
                    balanceTimestamp,
                    dayBalanceMap.keySet()
            );

            // Clear the balance map for the next day
            dayBalanceMap.clear();
            self.updateLastProcessedTimestamp(startOfDayToProcess);
            startOfDayToProcess = InstantUtil.getStartOfNextDay(startOfDayToProcess);
            endOfDayToProcess = InstantUtil.getEndOfDay(startOfDayToProcess);
        }
        return null;
    }


    @Transactional
    public void updateLastProcessedTimestamp(Instant lastProcessedTimestamp) {
        oneTokenLastTimestampFinderService.findAll()
                .stream()
                .findFirst()
                .ifPresentOrElse(oneTokenLastTimestamp -> oneTokenLastTimestamp.setLastProcessedTimestamp(lastProcessedTimestamp),
                        () -> oneTokenLastTimestampCreateService.create(lastProcessedTimestamp));
    }

    @Transactional
    public void fillGapsForAddressesWithoutTransfers(Instant previousTimestamp,
                                                     Instant currentTimestamp,
                                                     Set<String> addressesWithTransfers) {
        int pageNumber = 0;
        Page<OneTokenBalanceSeries> page;

        // Find the most recent timestamp that has balances (going backwards from previousTimestamp)
        Instant timestampToLookFor = findMostRecentTimestampWithBalances(previousTimestamp);
        if (timestampToLookFor == null) {
            return; // No balances found to copy
        }

        do {
            Pageable pageable = PageRequest.of(pageNumber, GAP_PROCESSING_PAGE_SIZE);
            page = oneTokenBalanceSeriesFinderService.findAllByTimestampAt(timestampToLookFor, pageable);

            List<OneTokenBalanceSeries> addressesToProcess = page.getContent()
                    .stream()
                    .filter(oneTokenBalanceSeries -> !addressesWithTransfers.contains(oneTokenBalanceSeries.getAddress()))
                    .toList();

            for (OneTokenBalanceSeries oneTokenBalanceSeries : addressesToProcess) {
                oneTokenBalanceSeriesCreateService.create(
                        oneTokenBalanceSeries.getAddress(),
                        oneTokenBalanceSeries.getAmount(),
                        currentTimestamp
                );
            }

            pageNumber++;
        } while (page.hasNext());
    }

    private Instant findMostRecentTimestampWithBalances(Instant startingTimestamp) {
        Instant currentTimestamp = startingTimestamp;
        Instant tgeTimestamp = tokenProperties.one().tokenTgeTimestamp();

        // Look backwards from startingTimestamp until we find a day with balances or reach TGE
        while (!currentTimestamp.isBefore(InstantUtil.getStartOfDay(tgeTimestamp))) {
            if (oneTokenBalanceSeriesFinderService.existsByTimestampAt(currentTimestamp)) {
                return currentTimestamp;
            }
            currentTimestamp = InstantUtil.getPreviousStartOfDay(currentTimestamp);
        }

        return null; // No balances found
    }
}
