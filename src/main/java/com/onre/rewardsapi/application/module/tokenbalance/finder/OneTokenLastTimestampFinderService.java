package com.onre.rewardsapi.application.module.tokenbalance.finder;

import com.onre.rewardsapi.application.common.finder.BaseFinderService;
import com.onre.rewardsapi.application.module.tokenbalance.exception.OneTokenLastBalanceNotFoundException;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenLastTimestamp;
import com.onre.rewardsapi.domain.tokenbalance.repository.OneTokenLastTimestampRepository;
import org.springframework.stereotype.Service;

@Service
public class OneTokenLastTimestampFinderService extends BaseFinderService<OneTokenLastTimestamp> {

    protected OneTokenLastTimestampFinderService(OneTokenLastTimestampRepository repository) {
        super(repository);
    }

    @Override
    protected RuntimeException createNotFoundException(String message) {
        return new OneTokenLastBalanceNotFoundException(message);
    }

    @Override
    protected Class<OneTokenLastTimestamp> getEntityType() {
        return OneTokenLastTimestamp.class;
    }
}
