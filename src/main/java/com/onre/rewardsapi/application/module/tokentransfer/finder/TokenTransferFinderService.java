package com.onre.rewardsapi.application.module.tokentransfer.finder;

import com.onre.rewardsapi.application.common.finder.BaseFinderService;
import com.onre.rewardsapi.application.module.tokentransfer.exception.TokenTransferNotFoundException;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransfer;
import com.onre.rewardsapi.domain.tokentransfer.repository.TokenTransferRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Service
public class TokenTransferFinderService extends BaseFinderService<TokenTransfer> {

    private final TokenTransferRepository tokenTransferRepository;

    protected TokenTransferFinderService(TokenTransferRepository repository) {
        super(repository);
        this.tokenTransferRepository = repository;
    }

    @Override
    protected RuntimeException createNotFoundException(String message) {
        return new TokenTransferNotFoundException(message);
    }

    @Override
    protected Class<TokenTransfer> getEntityType() {
        return TokenTransfer.class;
    }

    @Transactional(readOnly = true)
    public List<TokenTransfer> findAllByInstantBetween(Instant start, Instant end) {
        return tokenTransferRepository.findAllByTimestampBetween(start, end);
    }
}
