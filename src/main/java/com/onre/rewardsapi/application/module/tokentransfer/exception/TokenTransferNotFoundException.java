package com.onre.rewardsapi.application.module.tokentransfer.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class TokenTransferNotFoundException extends ApiException {

    public TokenTransferNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return SolanaTokenTransferPageErrorType.TOKEN_TRANSFER_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.TOKEN_TRANSFER;
    }
}
