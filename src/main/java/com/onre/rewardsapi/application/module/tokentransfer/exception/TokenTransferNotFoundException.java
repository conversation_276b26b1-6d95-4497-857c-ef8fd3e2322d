package com.onre.rewardsapi.application.module.tokentransfer.exception;

import com.onre.rewardsapi.infrastructure.exception.ApiException;
import com.onre.rewardsapi.infrastructure.exception.ErrorType;

public class TokenTransferNotFoundException extends ApiException {

    public TokenTransferNotFoundException(String message) {
        super(message);
    }

    @Override
    protected ErrorType getErrorType() {
        return SolanaTokenTransferPageErrorType.TOKEN_TRANSFER_NOT_FOUND;
    }

    @Override
    protected Module getModule() {
        return Module.TOKEN_TRANSFER;
    }
}
