package com.onre.rewardsapi.application.module.tokentransfer.exception;

import com.onre.rewardsapi.infrastructure.exception.ErrorType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


@Getter
@RequiredArgsConstructor
public enum SolanaTokenTransferPageErrorType implements ErrorType {

    SOLANA_TOKEN_TRANSFER_PAGE_NOT_FOUND("100", "Solana token transfer page not found."),
    TOKEN_TRANSFER_NOT_FOUND("101", "Token transfer not found.");


    private final String code;

    /**
     * Error custom message
     */
    private final String message;
}
