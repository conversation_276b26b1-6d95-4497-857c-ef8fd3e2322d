package com.onre.rewardsapi.application.module.tokenbalance.exception;

import com.onre.rewardsapi.infrastructure.exception.ErrorType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;


@Getter
@RequiredArgsConstructor
public enum TokenBalanceErrorType implements ErrorType {

    ONE_TOKEN_LAST_TIMESTAMP_NOT_FOUND("100", "One token last timestamp not found."),
    ONE_TOKEN_BALANCE_SERIES_NOT_FOUND("101", "One token balance series not found.");


    private final String code;

    /**
     * Error custom message
     */
    private final String message;
}
