package com.onre.rewardsapi.application.common.properties;


import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;

@Validated
@ConfigurationProperties(prefix = "onre.token")
public record TokenProperties(
        @NotNull Token one
) {
    public record Token(
            @NotNull String address,
            @NotNull Long tokenBlockTge,
            @NotNull Instant tokenTgeTimestamp
    ) {
    }
}
