package com.onre.rewardsapi.application.common.util;

import java.time.Instant;

public class InstantUtil {

    private static final long SECONDS_IN_DAY = 24 * 60 * 60L;

    public static Instant getStartOfDay(Instant instant) {
        return Instant.ofEpochSecond(instant.getEpochSecond() / SECONDS_IN_DAY * SECONDS_IN_DAY);
    }

    public static Instant getStartOfNextDay(Instant instant) {
        return getStartOfDay(instant).plusSeconds(SECONDS_IN_DAY);
    }

    public static Instant getEndOfDay(Instant instant) {
        return getStartOfNextDay(instant).minusSeconds(1);
    }

    public static Instant getPreviousStartOfDay(Instant instant) {
        return getStartOfDay(instant).minusSeconds(SECONDS_IN_DAY);
    }
}
