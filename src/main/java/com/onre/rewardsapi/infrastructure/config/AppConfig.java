
package com.onre.rewardsapi.infrastructure.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.time.Clock;

@Configuration
@EnableScheduling
@EnableAsync
@RequiredArgsConstructor
@ComponentScan(basePackages = "com.onre.rewardsapi")
@ConfigurationPropertiesScan(basePackages = "com.onre.rewardsapi")
@EnableAspectJAutoProxy
public class AppConfig {

    @Bean
    public Clock clock() {
        return Clock.systemUTC();
    }
}
