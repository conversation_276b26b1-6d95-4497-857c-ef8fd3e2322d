package com.onre.rewardsapi.infrastructure.jobs;

import com.onre.rewardsapi.adapter.in.quartz.QuartzJob;
import com.onre.rewardsapi.infrastructure.jobs.properties.QuartzProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronScheduleBuilder;
import org.quartz.Job;
import org.quartz.JobBuilder;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.quartz.TriggerBuilder;
import org.quartz.TriggerKey;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Configuration class for scheduling Quartz jobs.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(value = {"quartz.enabled"}, havingValue = "true", matchIfMissing = false)
public class QuartzJobConfig {

    private final List<QuartzJob> quartzJobs;
    private final QuartzProperties quartzProperties;
    private final Scheduler scheduler;

    /**
     * Schedule jobs after the application context is initialized.
     */
    @EventListener(value = ApplicationReadyEvent.class)
    public void scheduleJobs() {
        try {
            Map<Boolean, List<QuartzJob>> partitionedJobs = quartzJobs.stream()
                    .collect(Collectors.partitioningBy(job ->
                            quartzProperties.getJobProperties(job.getJobType()).enabled()));

            List<QuartzJob> enabledJobs = partitionedJobs.get(true);
            List<QuartzJob> disabledJobs = partitionedJobs.get(false);

            disabledJobs.forEach(this::deleteJobIfExists);
            enabledJobs.forEach(this::scheduleOrUpdateJob);

            log.info("Scheduled {} enabled jobs, disabled {} jobs", enabledJobs.size(), disabledJobs.size());
        } catch (Exception e) {
            log.error("Error scheduling jobs", e);
            throw new RuntimeException("Failed to schedule jobs", e);
        }
    }

    /**
     * Delete a job if it exists in the scheduler.
     *
     * @param job the job to delete
     */
    private void deleteJobIfExists(QuartzJob job) {
        try {
            JobKey jobKey = JobKey.jobKey(job.getJobName(), job.getJobGroup());
            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
                log.info("Deleted job: {}", job.getJobName());
            }
        } catch (SchedulerException e) {
            log.error("Error deleting job: {}", job.getJobName(), e);
        }
    }

    /**
     * Schedule or update a job in the scheduler.
     *
     * @param job the job to schedule
     */
    private void scheduleOrUpdateJob(QuartzJob job) {
        try {
            JobKey jobKey = JobKey.jobKey(job.getJobName(), job.getJobGroup());
            TriggerKey triggerKey = TriggerKey.triggerKey(job.getJobName() + "-trigger", job.getJobGroup());

            var jobDetail = JobBuilder.newJob(unwrapTargetClass(job))
                    .withIdentity(jobKey)
                    .withDescription(job.getDescription())
                    .storeDurably()
                    .build();

            String cronExpression = quartzProperties.getJobProperties(job.getJobType()).cron();

            var trigger = TriggerBuilder.newTrigger()
                    .withIdentity(triggerKey)
                    .forJob(jobDetail)
                    .withSchedule(
                            CronScheduleBuilder.cronSchedule(cronExpression)
                                    .withMisfireHandlingInstructionDoNothing()
                    )
                    .build();

            // Delete existing job if it exists
            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
            }

            scheduler.scheduleJob(jobDetail, trigger);
            log.info("Scheduled job: {} with cron: {}", job.getJobName(), cronExpression);
        } catch (SchedulerException e) {
            log.error("Error scheduling job: {}", job.getJobName(), e);
        }
    }

    /**
     * Unwrap the target class from Spring AOP proxy.
     *
     * @param bean the bean to unwrap
     * @return the target class
     */
    @SuppressWarnings("unchecked")
    private Class<Job> unwrapTargetClass(Object bean) {
        return (Class<Job>) AopProxyUtils.ultimateTargetClass(bean);
    }
}
