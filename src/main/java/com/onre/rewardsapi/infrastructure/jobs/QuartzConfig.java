package com.onre.rewardsapi.infrastructure.jobs;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = {"quartz.enabled"}, havingValue = "true", matchIfMissing = false)
@RequiredArgsConstructor
public class QuartzConfig {
    private final AutowiringSpringBeanJobFactory jobFactory;

    @Bean
    public SchedulerFactoryBeanCustomizer schedulerFactoryBeanCustomizer() {
        return bean -> {
            bean.setJobFactory(jobFactory);
            // Ensure auto-startup is enabled when quartz is enabled
            bean.setAutoStartup(true);
        };
    }
}
