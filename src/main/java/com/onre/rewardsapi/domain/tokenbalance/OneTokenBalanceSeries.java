package com.onre.rewardsapi.domain.tokenbalance;

import com.onre.rewardsapi.domain.CreatableEntity;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class OneTokenBalanceSeries extends CreatableEntity {
    private String address;
    private BigInteger amount;
    private Instant timestamp;

    public static OneTokenBalanceSeries create(String address, BigInteger amount, Instant timestamp) {
        return new OneTokenBalanceSeries(address, amount, timestamp);
    }
}
