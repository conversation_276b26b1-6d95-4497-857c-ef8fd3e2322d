package com.onre.rewardsapi.domain.tokenbalance.repository;

import com.onre.rewardsapi.domain.tokenbalance.OneTokenBalanceSeries;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface OneTokenBalanceSeriesRepository extends JpaRepository<OneTokenBalanceSeries, UUID> {
    Optional<OneTokenBalanceSeries> findByAddressAndTimestamp(String address, Instant timestamp);

    List<OneTokenBalanceSeries> findAllByTimestamp(Instant startOfDayToProcess);

    Page<OneTokenBalanceSeries> findAllByTimestamp(Instant timestamp, Pageable pageable);
}
