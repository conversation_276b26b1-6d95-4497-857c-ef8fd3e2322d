package com.onre.rewardsapi.domain.tokenbalance;

import com.onre.rewardsapi.domain.tokenbalance.repository.OneTokenLastTimestampRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.Instant;

@Service
@RequiredArgsConstructor
public class OneTokenLastTimestampCreateService {

    private final OneTokenLastTimestampRepository oneTokenLastTimestampRepository;

    @Transactional
    public OneTokenLastTimestamp create(Instant timestamp) {
        return oneTokenLastTimestampRepository.save(OneTokenLastTimestamp.create(timestamp));
    }
}
