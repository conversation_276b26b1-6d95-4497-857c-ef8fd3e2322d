package com.onre.rewardsapi.domain.tokenbalance;

import com.onre.rewardsapi.domain.UpdatableEntity;
import jakarta.persistence.Entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class OneTokenLastTimestamp extends UpdatableEntity {
    private Instant lastProcessedTimestamp;

    public static OneTokenLastTimestamp create(Instant lastProcessedTimestamp) {
        return new OneTokenLastTimestamp(lastProcessedTimestamp);
    }

    public void updateLastProcessedTimestamp(Instant lastProcessedTimestamp) {
        this.lastProcessedTimestamp = lastProcessedTimestamp;
    }
}
