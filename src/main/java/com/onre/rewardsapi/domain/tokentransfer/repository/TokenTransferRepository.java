package com.onre.rewardsapi.domain.tokentransfer.repository;

import com.onre.rewardsapi.domain.tokentransfer.TokenTransfer;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public interface TokenTransferRepository extends JpaRepository<TokenTransfer, UUID> {
    List<TokenTransfer> findAllByTimestampBetween(Instant start, Instant end);
}
