package com.onre.rewardsapi.adapter.in.quartz.jobs;

import com.onre.rewardsapi.adapter.in.quartz.JobType;
import com.onre.rewardsapi.adapter.in.quartz.QuartzJob;
import com.onre.rewardsapi.application.common.command.Command;
import com.onre.rewardsapi.application.module.tokenbalance.command.ProcessDailyTokenBalanceCommand;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "quartz.enabled", havingValue = "true", matchIfMissing = false)
@DisallowConcurrentExecution
public class ProcessDailyTokenBalanceJob extends QuartzJob {
    @Override
    public JobType getJobType() {
        return JobType.PROCESS_DAILY_TOKEN_BALANCE;
    }

    @Override
    public Command<?> createCommand(JobExecutionContext context) {
        return new ProcessDailyTokenBalanceCommand();
    }
}
