package com.onre.rewardsapi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.context.annotation.Import;

@SpringBootApplication(exclude = {QuartzAutoConfiguration.class})
@Import(QuartzConditionalConfiguration.class)
public class OnreRewardsApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(OnreRewardsApiApplication.class, args);
    }

    @ConditionalOnProperty(value = "quartz.enabled", havingValue = "true", matchIfMissing = false)
    @Import(QuartzAutoConfiguration.class)
    static class QuartzConditionalConfiguration {
    }
}
