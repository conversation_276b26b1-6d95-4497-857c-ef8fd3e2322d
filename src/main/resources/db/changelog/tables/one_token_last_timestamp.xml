<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="create_one_token_last_timestamp_table" author="<PERSON>-<PERSON>" runOnChange="false">
        <preConditions onSqlOutput="TEST" onFail="CONTINUE">
            <not>
                <tableExists tableName="one_token_last_timestamp"/>
            </not>
        </preConditions>

        <createTable tableName="one_token_last_timestamp">
            <column name="id" type="uuid">
                <constraints primaryKey="true" primaryKeyName="PK_one_token_last_timestamp"/>
            </column>
            <column name="last_processed_timestamp" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>