<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">


    <changeSet id="create_one_token_balance_series_table" author="<PERSON>-<PERSON>" runOnChange="false">
        <preConditions onSqlOutput="TEST" onFail="CONTINUE">
            <not>
                <tableExists tableName="one_token_balance_series"/>
            </not>
        </preConditions>

        <createTable tableName="one_token_balance_series">
            <column name="id" type="uuid">
                <constraints primaryKey="true" primaryKeyName="PK_one_token_balance_series"/>
            </column>
            <column name="address" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="numeric">
                <constraints nullable="false"/>
            </column>
            <column name="timestamp" type="timestamptz">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="add_one_token_balance_series_address_unique_index" author="Marian-Daniel Rolnik">
        <createIndex tableName="one_token_balance_series" indexName="one_token_balance_series_address_unique_index">
            <column name="address"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>