package com.onre.rewardsapi.common;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * Utility class for working with test times in a readable way.
 * Provides methods to create test scenarios with clear time relationships.
 */
public class TestTimeHelper {

    /**
     * Base test time - represents the TGE (Token Generation Event) timestamp.
     * All other times are calculated relative to this.
     */
    public static final Instant TGE_TIME = Instant.parse("2023-01-01T00:00:00Z");

    /**
     * Gets the start of a specific day relative to TGE.
     * Day 0 = TGE day, Day 1 = day after TGE, etc.
     */
    public static Instant dayStart(int dayOffset) {
        return TGE_TIME.plus(dayOffset, ChronoUnit.DAYS);
    }

    /**
     * Gets a time within a specific day.
     * @param dayOffset Days after TGE (0 = TGE day)
     * @param hourOffset Hours into the day (0-23)
     */
    public static Instant dayTime(int dayOffset, int hourOffset) {
        return dayStart(dayOffset).plus(hourOffset, ChronoUnit.HOURS);
    }

    /**
     * Gets the end of a specific day (23:59:59).
     */
    public static Instant dayEnd(int dayOffset) {
        return dayStart(dayOffset + 1).minus(1, ChronoUnit.SECONDS);
    }

    /**
     * Creates a test scenario description for better test readability.
     */
    public static class TestScenario {
        private final Instant now;
        private final String description;

        public TestScenario(Instant now, String description) {
            this.now = now;
            this.description = description;
        }

        public Instant getNow() {
            return now;
        }

        public String getDescription() {
            return description;
        }

        public Instant getYesterday() {
            return now.minus(1, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS);
        }

        public Instant getToday() {
            return now.truncatedTo(ChronoUnit.DAYS);
        }

        @Override
        public String toString() {
            return String.format("TestScenario{now=%s, description='%s'}", now, description);
        }
    }

    /**
     * Common test scenarios for different testing needs.
     */
    public static class Scenarios {
        
        /**
         * Standard scenario: "now" is 6 days after TGE.
         * This allows processing of days 0-5 (TGE to 5 days later).
         */
        public static TestScenario standard() {
            return new TestScenario(
                dayStart(6), // 2023-01-07T00:00:00Z
                "Standard scenario: 6 days after TGE, can process days 0-5"
            );
        }

        /**
         * Early scenario: "now" is 2 days after TGE.
         * This allows processing of days 0-1 only.
         */
        public static TestScenario early() {
            return new TestScenario(
                dayStart(2), // 2023-01-03T00:00:00Z
                "Early scenario: 2 days after TGE, can process days 0-1"
            );
        }

        /**
         * Custom scenario with specific "now" time.
         */
        public static TestScenario custom(Instant now, String description) {
            return new TestScenario(now, description);
        }
    }
}
