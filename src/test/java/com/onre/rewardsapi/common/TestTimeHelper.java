package com.onre.rewardsapi.common;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * Utility class for working with test times in a readable way.
 * Provides methods to create test scenarios with explicit time configuration.
 */
public class TestTimeHelper {

    // Thread-local storage for current test scenario
    private static final ThreadLocal<TestScenario> currentScenario = new ThreadLocal<>();

    /**
     * Sets the current test scenario for this thread.
     * This allows dayStart() and dayTime() methods to work relative to the scenario's TGE.
     */
    public static void setCurrentScenario(TestScenario scenario) {
        currentScenario.set(scenario);
    }

    /**
     * Clears the current test scenario for this thread.
     */
    public static void clearCurrentScenario() {
        currentScenario.remove();
    }

    /**
     * Gets the start of a specific day relative to the current scenario's TGE.
     * Day 0 = TGE day, Day 1 = day after TGE, etc.
     * Requires a test scenario to be set via setCurrentScenario().
     */
    public static Instant dayStart(int dayOffset) {
        TestScenario scenario = currentScenario.get();
        if (scenario == null) {
            throw new IllegalStateException("No test scenario set. Call setCurrentScenario() first.");
        }
        return scenario.getTgeTime().plus(dayOffset, ChronoUnit.DAYS);
    }

    /**
     * Gets a specific time on a specific day relative to the current scenario's TGE.
     * Day 0 = TGE day, Day 1 = day after TGE, etc.
     * Hour is in 24-hour format (0-23).
     * Requires a test scenario to be set via setCurrentScenario().
     */
    public static Instant dayTime(int dayOffset, int hour) {
        return dayStart(dayOffset).plus(hour, ChronoUnit.HOURS);
    }

    /**
     * Gets the end of a specific day (23:59:59).
     * Requires a test scenario to be set via setCurrentScenario().
     */
    public static Instant dayEnd(int dayOffset) {
        return dayStart(dayOffset + 1).minus(1, ChronoUnit.SECONDS);
    }

    /**
     * Creates a test scenario with explicit time configuration.
     * Both TGE and "now" timestamps are configurable from the test itself.
     */
    public static class TestScenario {
        private final Instant tgeTime;
        private final Instant now;
        private final String description;

        public TestScenario(Instant tgeTime, Instant now, String description) {
            this.tgeTime = tgeTime;
            this.now = now;
            this.description = description;
        }

        /**
         * Create a test scenario with explicit TGE and "now" timestamps.
         * This makes all time configuration visible directly in the test.
         *
         * @param tgeTime The TGE (Token Generation Event) timestamp
         * @param now The current time ("now") for the test
         * @param description Description of what this scenario represents
         */
        public static TestScenario withTimes(Instant tgeTime, Instant now, String description) {
            return new TestScenario(tgeTime, now, description);
        }

        /**
         * Create a test scenario with explicit TGE and "now" as strings for readability.
         *
         * @param tgeTime The TGE timestamp as ISO string (e.g., "2023-01-01T00:00:00Z")
         * @param now The current time as ISO string (e.g., "2023-01-03T00:00:00Z")
         * @param description Description of what this scenario represents
         */
        public static TestScenario withTimes(String tgeTime, String now, String description) {
            return new TestScenario(
                Instant.parse(tgeTime),
                Instant.parse(now),
                description
            );
        }

        public Instant getTgeTime() {
            return tgeTime;
        }

        public Instant getNow() {
            return now;
        }

        public String getDescription() {
            return description;
        }

        public Instant getYesterday() {
            return now.minus(1, ChronoUnit.DAYS).truncatedTo(ChronoUnit.DAYS);
        }

        public Instant getToday() {
            return now.truncatedTo(ChronoUnit.DAYS);
        }

        @Override
        public String toString() {
            return String.format("TestScenario{tgeTime=%s, now=%s, description='%s'}", tgeTime, now, description);
        }
    }

    /**
     * Common test scenarios for different testing needs.
     * These are convenience methods, but tests should prefer creating their own scenarios
     * using TestScenario.withTimes() for explicit and visible time configuration.
     */
    public static class Scenarios {

        /**
         * Standard scenario: TGE on 2023-01-01, "now" is 2023-01-03.
         * This allows processing of days 2023-01-01 and 2023-01-02.
         * Minimal range to avoid performance issues.
         */
        public static TestScenario standard() {
            return TestScenario.withTimes(
                "2023-01-01T00:00:00Z",  // TGE
                "2023-01-03T00:00:00Z",  // Now (2 days later)
                "Standard scenario: 2 days processing window"
            );
        }

        /**
         * Early scenario: TGE on 2023-01-01, "now" is 2023-01-02.
         * This allows processing of day 2023-01-01 only.
         */
        public static TestScenario early() {
            return TestScenario.withTimes(
                "2023-01-01T00:00:00Z",  // TGE
                "2023-01-02T00:00:00Z",  // Now (1 day later)
                "Early scenario: 1 day processing window"
            );
        }

        /**
         * Extended scenario: TGE on 2023-01-01, "now" is 2023-01-06.
         * This allows processing of days 2023-01-01 through 2023-01-05.
         * Use for tests that need to process more days.
         */
        public static TestScenario extended() {
            return TestScenario.withTimes(
                "2023-01-01T00:00:00Z",  // TGE
                "2023-01-06T00:00:00Z",  // Now (5 days later)
                "Extended scenario: 5 days processing window"
            );
        }
    }
}
