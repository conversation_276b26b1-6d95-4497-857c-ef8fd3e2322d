package com.onre.rewardsapi.common;

import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import com.onre.rewardsapi.domain.solanatokentransferpage.repository.SolanaTokenTransferPageRepository;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransfer;
import com.onre.rewardsapi.domain.tokentransfer.repository.TokenTransferRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.time.Instant;

/**
 * Helper class for creating test data in the database for integration tests.
 * This class provides methods to create various entities needed for testing.
 * Only available in the test profile.
 */
@Component
@RequiredArgsConstructor
@Profile("test")
public class TestDataHelper {

    private final SolanaTokenTransferPageRepository solanaTokenTransferPageRepository;
    private final TokenTransferRepository tokenTransferRepository;

    /**
     * Creates a SolanaTokenTransferPage with the specified parameters
     */
    public SolanaTokenTransferPage createSolanaTokenTransferPage(
            Integer lastProcessedPage,
            String lastProcessedTransactionSignature,
            Boolean completePageProcessed
    ) {
        SolanaTokenTransferPage page = SolanaTokenTransferPage.create(
                lastProcessedPage,
                lastProcessedTransactionSignature,
                completePageProcessed
        );
        return solanaTokenTransferPageRepository.save(page);
    }

    /**
     * Creates a TokenTransfer with the specified parameters
     */
    public TokenTransfer createTokenTransfer(
            String fromSolAddress,
            String toSolAddress,
            String fromTokenAccount,
            String toTokenAccount,
            String signature,
            BigInteger amount,
            Instant timestamp
    ) {
        TokenTransfer tokenTransfer = new TokenTransfer(
                fromSolAddress,
                toSolAddress,
                fromTokenAccount,
                toTokenAccount,
                signature,
                amount,
                timestamp
        );
        return tokenTransferRepository.save(tokenTransfer);
    }
}
