package com.onre.rewardsapi.config;

import com.onre.rewardsapi.application.common.properties.TokenProperties;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;

@TestConfiguration
public class TestClockConfig {

    private volatile Clock currentClock = Clock.systemUTC();
    private volatile TokenProperties currentTokenProperties = null;

    /**
     * Provides a configurable clock for tests.
     * By default uses system clock, but can be overridden per test.
     */
    @Bean
    @Primary
    public Clock testClock() {
        // Return a clock that delegates to the current clock
        // This allows us to change the clock during test execution
        return new Clock() {
            @Override
            public ZoneId getZone() {
                return currentClock.getZone();
            }

            @Override
            public Clock withZone(ZoneId zone) {
                return currentClock.withZone(zone);
            }

            @Override
            public Instant instant() {
                return currentClock.instant();
            }
        };
    }

    /**
     * Provides configurable token properties for tests.
     * Can be overridden per test to control TGE timestamp.
     */
    @Bean
    @Primary
    public TokenProperties testTokenProperties() {
        if (currentTokenProperties != null) {
            return currentTokenProperties;
        }
        // Default test token properties
        return new TokenProperties(
            new TokenProperties.Token(
                "5Y8NV33Vv7WbnLfq3zBcKSdYPrk7g2KoiQoe7M2tcxp5",
                331308318L,
                Instant.parse("2022-12-31T00:00:00Z")
            )
        );
    }

    /**
     * Sets a fixed clock for the current test.
     * This allows each test to control what "now" means.
     */
    public void setFixedClock(Instant fixedInstant) {
        this.currentClock = Clock.fixed(fixedInstant, ZoneOffset.UTC);
    }

    /**
     * Sets custom token properties for the current test.
     * This allows each test to control the TGE timestamp.
     */
    public void setTokenProperties(TokenProperties tokenProperties) {
        this.currentTokenProperties = tokenProperties;
    }

    /**
     * Resets the clock to system time and token properties to default.
     */
    public void resetClock() {
        this.currentClock = Clock.systemUTC();
        this.currentTokenProperties = null;
    }
}
