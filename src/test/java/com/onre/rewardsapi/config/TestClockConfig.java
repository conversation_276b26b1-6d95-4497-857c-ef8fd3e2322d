package com.onre.rewardsapi.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneOffset;

@TestConfiguration
public class TestClockConfig {

    /**
     * Provides a fixed clock for tests set to 2023-01-05T00:00:00Z.
     * This ensures that:
     * - "now" is 2023-01-05T00:00:00Z
     * - "yesterday" is 2023-01-04T00:00:00Z
     * - Test data from 2023-01-01 to 2023-01-03 will be processed
     * - Tests are deterministic and don't depend on actual current time
     */
    @Bean
    @Primary
    public Clock testClock() {
        // Set "now" to 2023-01-05T00:00:00Z so that test data from 2023-01-01 to 2023-01-03 gets processed
        Instant fixedInstant = Instant.parse("2023-01-05T00:00:00Z");
        return Clock.fixed(fixedInstant, ZoneOffset.UTC);
    }
}
