package com.onre.rewardsapi.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;

@TestConfiguration
public class TestClockConfig {

    private volatile Clock currentClock = Clock.systemUTC();

    /**
     * Provides a configurable clock for tests.
     * By default uses system clock, but can be overridden per test.
     */
    @Bean
    @Primary
    public Clock testClock() {
        // Return a clock that delegates to the current clock
        // This allows us to change the clock during test execution
        return new Clock() {
            @Override
            public ZoneId getZone() {
                return currentClock.getZone();
            }

            @Override
            public Clock withZone(ZoneId zone) {
                return currentClock.withZone(zone);
            }

            @Override
            public Instant instant() {
                return currentClock.instant();
            }
        };
    }

    /**
     * Sets a fixed clock for the current test.
     * This allows each test to control what "now" means.
     */
    public void setFixedClock(Instant fixedInstant) {
        this.currentClock = Clock.fixed(fixedInstant, ZoneOffset.UTC);
    }

    /**
     * Resets the clock to system time.
     */
    public void resetClock() {
        this.currentClock = Clock.systemUTC();
    }
}
