package com.onre.rewardsapi.application.module.tokentransfer;

import com.onre.rewardsapi.application.module.tokentransfer.command.ProcessTokenTransferCommand;
import com.onre.rewardsapi.application.module.tokentransfer.port.out.GetTokenTransfers;
import com.onre.rewardsapi.common.IntegrationTest;
import com.onre.rewardsapi.common.TestDataHelper;
import com.onre.rewardsapi.domain.solanatokentransferpage.SolanaTokenTransferPage;
import com.onre.rewardsapi.domain.solanatokentransferpage.repository.SolanaTokenTransferPageRepository;
import com.onre.rewardsapi.domain.tokentransfer.TokenTransfer;
import com.onre.rewardsapi.domain.tokentransfer.repository.TokenTransferRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class ProcessTokenTransfersCommandHandlerTest extends IntegrationTest {

    @MockitoBean
    private GetTokenTransfers getTokenTransfers;

    @Autowired
    private ProcessTokenTransfersCommandHandler handler;

    @Autowired
    private TestDataHelper testDataHelper;

    @Autowired
    private SolanaTokenTransferPageRepository solanaTokenTransferPageRepository;

    @Autowired
    private TokenTransferRepository tokenTransferRepository;

    private static final int DEFAULT_PAGE_SIZE = 100;

    private ProcessTokenTransferCommand command;

    @BeforeEach
    void setUp() {
        command = new ProcessTokenTransferCommand();
    }

    @Test
    void handle_FirstTimeProcessing_CreatesNewPageAndProcessesTransactions() {
        // Given
        // Mock API response with transactions (partial page - less than DEFAULT_PAGE_SIZE)
        List<GetTokenTransfers.Result.Transaction> transactions = createMockTransactions(3);
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(1, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_EmptyTransactionsResponse_StopsProcessing() {
        // Given
        // Mock API response with empty transactions (end of data)
        GetTokenTransfers.Result emptyResult = new GetTokenTransfers.Result(Collections.emptyList());
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(emptyResult);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(0, page.getLastProcessedPage());
        assertNull(page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_CompletePageProcessing_ContinuesToNextPage() {
        // Given
        // Mock API response with full page of transactions (DEFAULT_PAGE_SIZE)
        List<GetTokenTransfers.Result.Transaction> fullPageTransactions = createMockTransactions(DEFAULT_PAGE_SIZE);
        GetTokenTransfers.Result fullPageResult = new GetTokenTransfers.Result(fullPageTransactions);

        // Mock API response with partial page for the second call
        List<GetTokenTransfers.Result.Transaction> partialPageTransactions = createMockTransactions(3);
        GetTokenTransfers.Result partialPageResult = new GetTokenTransfers.Result(partialPageTransactions);

        // Setup mock to return full page first, then partial page
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class)))
            .thenReturn(fullPageResult)
            .thenReturn(partialPageResult);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(2, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_ResumeFromCompletePage_ContinuesFromNextPage() {
        // Given
        // Create a page with complete page processed
        testDataHelper.createSolanaTokenTransferPage(1, null, true);

        // Mock API response with transactions for page 2
        List<GetTokenTransfers.Result.Transaction> transactions = createMockTransactions(3);
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(2, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_ResumeFromIncompletePage_ContinuesFromSamePage() {
        // Given
        // Create a page with incomplete page processed and a last processed signature
        testDataHelper.createSolanaTokenTransferPage(3, "previous-signature", false);

        // Mock API response with transactions including the last processed one
        List<GetTokenTransfers.Result.Transaction> transactions = new ArrayList<>();
        transactions.add(createTransaction("previous-signature", "from-prev", "to-prev"));
        transactions.addAll(createMockTransactions(3));
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(3, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_LastProcessedSignatureNotFound_StartsFromBeginning() {
        // Given
        // Create a page with incomplete page processed and a last processed signature that won't be found
        testDataHelper.createSolanaTokenTransferPage(3, "non-existent-signature", false);

        // Mock API response with transactions that don't include the last processed one
        List<GetTokenTransfers.Result.Transaction> transactions = createMockTransactions(3);
        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(3, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_MultipleFullPages_ProcessesAllPages() {
        // Given
        // Mock API response with full pages for the first two calls, then a partial page
        List<GetTokenTransfers.Result.Transaction> fullPageTransactions1 = createMockTransactions(DEFAULT_PAGE_SIZE);
        GetTokenTransfers.Result fullPageResult1 = new GetTokenTransfers.Result(fullPageTransactions1);

        List<GetTokenTransfers.Result.Transaction> fullPageTransactions2 = createMockTransactions(DEFAULT_PAGE_SIZE);
        GetTokenTransfers.Result fullPageResult2 = new GetTokenTransfers.Result(fullPageTransactions2);

        List<GetTokenTransfers.Result.Transaction> partialPageTransactions = createMockTransactions(3);
        GetTokenTransfers.Result partialPageResult = new GetTokenTransfers.Result(partialPageTransactions);

        // Setup mock to return full pages first, then partial page
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class)))
            .thenReturn(fullPageResult1)
            .thenReturn(fullPageResult2)
            .thenReturn(partialPageResult);

        // When
        handler.handle(command);

        // Then
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(3, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void handle_MultipleTokenTransfersWithSameSignature_ProcessesAllTransfers() {
        // Given
        // Create transactions with the same signature but different from/to addresses
        List<GetTokenTransfers.Result.Transaction> transactions = new ArrayList<>();

        // Two transactions with the same signature
        String sameSignature = "same-signature";
        transactions.add(createTransaction(sameSignature, "from-1", "to-1"));
        transactions.add(createTransaction(sameSignature, "from-2", "to-2"));

        // Add a transaction with a different signature
        transactions.add(createTransaction("different-signature", "from-3", "to-3"));

        GetTokenTransfers.Result result = new GetTokenTransfers.Result(transactions);
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class))).thenReturn(result);

        // When
        handler.handle(command);

        // Then
        // Verify that all token transfers are saved to the database
        List<TokenTransfer> tokenTransfers = tokenTransferRepository.findAll();
        assertEquals(3, tokenTransfers.size());

        // Count transfers with the same signature
        long sameSignatureCount = tokenTransfers.stream()
                .filter(transfer -> sameSignature.equals(transfer.getTransactionSignature()))
                .count();
        assertEquals(2, sameSignatureCount);

        // Verify that the SolanaTokenTransferPage is correctly updated
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        assertEquals(1, page.getLastProcessedPage());
        assertEquals("different-signature", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }


    @Test
    void handle_FullPageFollowedByEmptyPage_MarksFullPageAsCompleteAndStopsProcessing() {
        // Given
        // Mock API response with exactly 100 transactions (full page)
        List<GetTokenTransfers.Result.Transaction> fullPageTransactions = createMockTransactions(DEFAULT_PAGE_SIZE);
        GetTokenTransfers.Result fullPageResult = new GetTokenTransfers.Result(fullPageTransactions);

        // For the second call (which should be for page 2), return empty page
        GetTokenTransfers.Result emptyResult = new GetTokenTransfers.Result(Collections.emptyList());

        // For the third call (which should be for the second handle), return 3 transfers
        List<GetTokenTransfers.Result.Transaction> nextPageTransactions = createMockTransactions(3);
        GetTokenTransfers.Result nextPageResult = new GetTokenTransfers.Result(nextPageTransactions);

        // Setup mock to return full page first, then empty page, then 3 transfers for second handle
        when(getTokenTransfers.invoke(any(GetTokenTransfers.Input.class)))
            .thenReturn(fullPageResult)
            .thenReturn(emptyResult)
            .thenReturn(nextPageResult);

        // When - First handle call
        handler.handle(command);

        // Then - Verify the page is marked as complete after first handle
        List<SolanaTokenTransferPage> pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        SolanaTokenTransferPage page = pages.getFirst();
        // Verify the page is marked as complete and the lastProcessedPage is 1 (not decremented)
        assertEquals(1, page.getLastProcessedPage());
        assertNull(page.getLastProcessedTransactionSignature());
        assertTrue(page.getCompletePageProcessed());

        // When - Second handle call
        handler.handle(command);

        // Then - Verify it processed the 3 transfers in the second handle
        pages = solanaTokenTransferPageRepository.findAll();
        assertEquals(1, pages.size());

        page = pages.getFirst();
        // Verify the page number and signature after second handle
        assertEquals(2, page.getLastProcessedPage());
        assertEquals("signature-2", page.getLastProcessedTransactionSignature());
        assertFalse(page.getCompletePageProcessed());
    }

    @Test
    void getCommandType_ReturnsCorrectCommandClass() {
        // When
        Class<ProcessTokenTransferCommand> commandType = handler.getCommandType();

        // Then
        assertEquals(ProcessTokenTransferCommand.class, commandType);
    }

    private List<GetTokenTransfers.Result.Transaction> createMockTransactions(int count) {
        return java.util.stream.IntStream.range(0, count)
                .mapToObj(i -> createTransaction("signature-" + i, "from-" + i, "to-" + i))
                .toList();
    }

    private GetTokenTransfers.Result.Transaction createTransaction(String signature, String fromAddress, String toAddress) {
        return new GetTokenTransfers.Result.Transaction(
                signature,
                fromAddress,
                toAddress,
                "fromTokenAccount",
                "toTokenAccount",
                BigInteger.valueOf(1000),
                Instant.now()
        );
    }
}
