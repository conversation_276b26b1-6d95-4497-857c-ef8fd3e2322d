package com.onre.rewardsapi.application.module.tokenbalance;

import com.onre.rewardsapi.application.module.tokenbalance.command.ProcessDailyTokenBalanceCommand;
import com.onre.rewardsapi.common.IntegrationTest;
import com.onre.rewardsapi.common.TestDataHelper;
import com.onre.rewardsapi.common.TestTimeHelper;
import com.onre.rewardsapi.config.TestClockConfig;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenBalanceSeries;
import com.onre.rewardsapi.domain.tokenbalance.OneTokenLastTimestamp;
import com.onre.rewardsapi.domain.tokenbalance.repository.OneTokenBalanceSeriesRepository;
import com.onre.rewardsapi.domain.tokenbalance.repository.OneTokenLastTimestampRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.math.BigInteger;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

import static com.onre.rewardsapi.common.TestTimeHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Import(TestClockConfig.class)
class ProcessDailyTokenBalanceCommandHandlerTest extends IntegrationTest {

    @Autowired
    private ProcessDailyTokenBalanceCommandHandler handler;

    @Autowired
    private TestDataHelper testDataHelper;

    @Autowired
    private OneTokenBalanceSeriesRepository oneTokenBalanceSeriesRepository;

    @Autowired
    private OneTokenLastTimestampRepository oneTokenLastTimestampRepository;

    private ProcessDailyTokenBalanceCommand command;

    @BeforeEach
    void setUp() {
        command = new ProcessDailyTokenBalanceCommand();
    }

    @Test
    void handle_FirstTimeProcessing_CreatesBalancesFromTgeTimestamp() {
        // Given
        // Create token transfers for the first day after TGE
        Instant tgeTimestamp = Instant.parse("2023-01-01T00:00:00Z");
        Instant day1Start = tgeTimestamp.truncatedTo(ChronoUnit.DAYS);

        // Create a transfer from address1 to address2
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify that balances were created for both addresses on the next day (day2)
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Balance.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(BigInteger.valueOf(100), address2Balance.get().getAmount());

        // Verify that the last processed timestamp was updated to yesterday (since today is not processed)
        List<OneTokenLastTimestamp> lastTimestamps = oneTokenLastTimestampRepository.findAll();
        assertEquals(1, lastTimestamps.size());

        // With our test clock, "now" is 2023-01-06T00:00:00Z, so "yesterday" is 2023-01-05T00:00:00Z
        Instant yesterday = Instant.parse("2023-01-05T00:00:00Z");
        assertEquals(yesterday, lastTimestamps.getFirst().getLastProcessedTimestamp());
    }

    @Test
    void handle_ResumeFromPreviousTimestamp_ContinuesFromNextDay() {
        // Given
        // Create a last processed timestamp for day 1
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create the last processed timestamp for day 1
        createLastProcessedTimestamp(day1Start);

        // Create balances for day 1
        createBalance("address1", BigInteger.valueOf(-100), day1Start);
        createBalance("address2", BigInteger.valueOf(100), day1Start);

        // Create a transfer for day 2
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day2Start.plus(2, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify that balances were created for day 3 (next day after day 2 transfers)
        Instant day3Start = day2Start.plus(1, ChronoUnit.DAYS);
        List<OneTokenBalanceSeries> day3Balances = oneTokenBalanceSeriesRepository.findAllByTimestamp(day3Start);
        assertEquals(3, day3Balances.size());

        // Verify the balance for address1 (should be carried over from day 1)
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day3Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Balance.get().getAmount());

        // Verify the balance for address2 (should be 100 - 50 = 50)
        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day3Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Balance.get().getAmount());

        // Verify the balance for address3 (should be +50)
        Optional<OneTokenBalanceSeries> address3Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day3Start);
        assertTrue(address3Balance.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Balance.get().getAmount());

        // Verify that the last processed timestamp was updated to yesterday
        List<OneTokenLastTimestamp> lastTimestamps = oneTokenLastTimestampRepository.findAll();
        assertEquals(1, lastTimestamps.size());

        // With our test clock, "now" is 2023-01-06T00:00:00Z, so "yesterday" is 2023-01-05T00:00:00Z
        Instant yesterday = Instant.parse("2023-01-05T00:00:00Z");
        assertEquals(yesterday, lastTimestamps.getFirst().getLastProcessedTimestamp());
    }

    @Test
    void handle_MultipleTransfersInOneDay_CalculatesCorrectBalances() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");

        // Create multiple transfers in the same day
        // address1 -> address2: 100
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));

        // address2 -> address3: 50
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day1Start.plus(2, ChronoUnit.HOURS));

        // address3 -> address1: 20
        createTokenTransfer("address3", "address1", BigInteger.valueOf(20), day1Start.plus(3, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify the balances for all addresses on the next day (day2)
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        // address1: -100 + 20 = -80
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.valueOf(-80), address1Balance.get().getAmount());

        // address2: +100 - 50 = +50
        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Balance.get().getAmount());

        // address3: +50 - 20 = +30
        Optional<OneTokenBalanceSeries> address3Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day2Start);
        assertTrue(address3Balance.isPresent());
        assertEquals(BigInteger.valueOf(30), address3Balance.get().getAmount());
    }

    @Test
    void handle_ProcessingMultipleDays_CalculatesCorrectBalances() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Instant day3Start = day2Start.plus(1, ChronoUnit.DAYS);

        // Day 1: address1 -> address2: 100
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));

        // Day 2: address2 -> address3: 50
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day2Start.plus(2, ChronoUnit.HOURS));

        // Day 3: address3 -> address1: 20
        createTokenTransfer("address3", "address1", BigInteger.valueOf(20), day3Start.plus(3, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify day 2 balances (from day 1 transfers)
        // address1: -100
        Optional<OneTokenBalanceSeries> address1Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Day2.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day2.get().getAmount());

        // address2: +100
        Optional<OneTokenBalanceSeries> address2Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Day2.isPresent());
        assertEquals(BigInteger.valueOf(100), address2Day2.get().getAmount());

        // Verify day 3 balances (from day 2 transfers)
        // address1: -100 (carried over)
        Optional<OneTokenBalanceSeries> address1Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day3Start);
        assertTrue(address1Day3.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day3.get().getAmount());

        // address2: 100 - 50 = 50
        Optional<OneTokenBalanceSeries> address2Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day3Start);
        assertTrue(address2Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Day3.get().getAmount());

        // address3: +50
        Optional<OneTokenBalanceSeries> address3Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day3Start);
        assertTrue(address3Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Day3.get().getAmount());

        // Verify day 4 balances (from day 3 transfers)
        Instant day4Start = day3Start.plus(1, ChronoUnit.DAYS);
        // address1: -100 + 20 = -80
        Optional<OneTokenBalanceSeries> address1Day4 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day4Start);
        assertTrue(address1Day4.isPresent());
        assertEquals(BigInteger.valueOf(-80), address1Day4.get().getAmount());

        // address2: 50 (carried over)
        Optional<OneTokenBalanceSeries> address2Day4 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day4Start);
        assertTrue(address2Day4.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Day4.get().getAmount());

        // address3: 50 - 20 = 30
        Optional<OneTokenBalanceSeries> address3Day4 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day4Start);
        assertTrue(address3Day4.isPresent());
        assertEquals(BigInteger.valueOf(30), address3Day4.get().getAmount());
    }

    @Test
    void handle_GapFilling_CopiesBalancesForAddressesWithoutTransfers() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create balances for day 1
        createBalance("address1", BigInteger.valueOf(-100), day1Start);
        createBalance("address2", BigInteger.valueOf(100), day1Start);
        createBalance("address3", BigInteger.valueOf(50), day1Start);

        // Create a transfer for day 2 that only involves address1 and address2
        createTokenTransfer("address1", "address2", BigInteger.valueOf(30), day2Start.plus(2, ChronoUnit.HOURS));

        // Create the last processed timestamp for day 1
        createLastProcessedTimestamp(day1Start);

        // When
        handler.handle(command);

        // Then
        // Verify that address3's balance was carried over to day 3 even though it wasn't involved in any transfers
        Instant day3Start = day2Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address3Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day3Start);
        assertTrue(address3Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Day3.get().getAmount());
    }

    @Test
    void handle_NoTransfersInDay_CopiesAllBalancesFromPreviousDay() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create balances for day 1
        createBalance("address1", BigInteger.valueOf(-100), day1Start);
        createBalance("address2", BigInteger.valueOf(100), day1Start);
        createBalance("address3", BigInteger.valueOf(50), day1Start);

        // No transfers for day 2

        // Create the last processed timestamp for day 1
        createLastProcessedTimestamp(day1Start);

        // When
        handler.handle(command);

        // Then
        // Verify that all balances were carried over to day 3 (since no transfers on day 2)
        Instant day3Start = day2Start.plus(1, ChronoUnit.DAYS);
        List<OneTokenBalanceSeries> day3Balances = oneTokenBalanceSeriesRepository.findAllByTimestamp(day3Start);
        assertEquals(3, day3Balances.size());

        // Check each address's balance
        Optional<OneTokenBalanceSeries> address1Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day3Start);
        assertTrue(address1Day3.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day3.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day3Start);
        assertTrue(address2Day3.isPresent());
        assertEquals(BigInteger.valueOf(100), address2Day3.get().getAmount());

        Optional<OneTokenBalanceSeries> address3Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day3Start);
        assertTrue(address3Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Day3.get().getAmount());
    }

    @Test
    void handle_NoTransfersAtAll_CreatesLastTimestampOnly() {
        // Given
        // No transfers created

        // When
        handler.handle(command);

        // Then
        // Verify no balances were created
        List<OneTokenBalanceSeries> balances = oneTokenBalanceSeriesRepository.findAll();
        assertEquals(0, balances.size());

        // Verify last processed timestamp was created (set to yesterday since today is not processed)
        List<OneTokenLastTimestamp> lastTimestamps = oneTokenLastTimestampRepository.findAll();
        assertEquals(1, lastTimestamps.size());

        // With our test clock, "now" is 2023-01-06T00:00:00Z, so "yesterday" is 2023-01-05T00:00:00Z
        Instant yesterday = Instant.parse("2023-01-05T00:00:00Z");
        assertEquals(yesterday, lastTimestamps.getFirst().getLastProcessedTimestamp());
    }

    @Test
    void handle_TransfersOnSameDayAsToday_DoesNotProcessToday() {
        // Given
        // With our test clock, "today" is 2023-01-06T00:00:00Z
        Instant today = Instant.parse("2023-01-06T00:00:00Z");

        // Create a transfer for today
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), today.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify no balances were created (today should not be processed)
        List<OneTokenBalanceSeries> balances = oneTokenBalanceSeriesRepository.findAll();
        assertEquals(0, balances.size());
    }

    @Test
    void handle_SelfTransfer_CalculatesCorrectBalance() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");

        // Create a self transfer (address1 -> address1)
        createTokenTransfer("address1", "address1", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify that the balance for address1 is 0 (self transfer should cancel out) on the next day
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.ZERO, address1Balance.get().getAmount());
    }

    @Test
    void handle_LargeAmountTransfer_HandlesCorrectly() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        BigInteger largeAmount = new BigInteger("999999999999999999999999999");

        // Create a transfer with a very large amount
        createTokenTransfer("address1", "address2", largeAmount, day1Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify the balances handle large amounts correctly on the next day
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(largeAmount.negate(), address1Balance.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(largeAmount, address2Balance.get().getAmount());
    }

    @Test
    void handle_ZeroAmountTransfer_CreatesZeroBalances() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");

        // Create a transfer with zero amount
        createTokenTransfer("address1", "address2", BigInteger.ZERO, day1Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify that balances are created with zero amounts on the next day
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.ZERO, address1Balance.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(BigInteger.ZERO, address2Balance.get().getAmount());
    }

    @Test
    void getCommandType_ReturnsCorrectCommandClass() {
        // When
        Class<ProcessDailyTokenBalanceCommand> commandType = handler.getCommandType();

        // Then
        assertEquals(ProcessDailyTokenBalanceCommand.class, commandType);
    }

    private void createTokenTransfer(String fromAddress, String toAddress, BigInteger amount, Instant timestamp) {
        testDataHelper.createTokenTransfer(
                fromAddress,
                toAddress,
                "fromTokenAccount",
                "toTokenAccount",
                "signature-" + fromAddress + "-" + toAddress + "-" + timestamp.toEpochMilli(),
                amount,
                timestamp
        );
    }

    private void createBalance(String address, BigInteger amount, Instant timestamp) {
        OneTokenBalanceSeries balance = OneTokenBalanceSeries.create(address, amount, timestamp);
        oneTokenBalanceSeriesRepository.save(balance);
    }

    private void createLastProcessedTimestamp(Instant timestamp) {
        OneTokenLastTimestamp lastTimestamp = OneTokenLastTimestamp.create(timestamp);
        oneTokenLastTimestampRepository.save(lastTimestamp);
    }

    @Test
    void handle_TransfersAtDayBoundaries_ProcessesCorrectly() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day1End = day1Start.plus(1, ChronoUnit.DAYS).minus(1, ChronoUnit.SECONDS);
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create transfers at the very end of day 1 and very beginning of day 2
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1End);
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day2Start);

        // When
        handler.handle(command);

        // Then
        // Verify day 2 balances (from day 1 transfers)
        Optional<OneTokenBalanceSeries> address1Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Day2.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day2.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Day2.isPresent());
        assertEquals(BigInteger.valueOf(100), address2Day2.get().getAmount());

        // Verify day 3 balances (from day 2 transfers)
        Instant day3Start = day2Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day3Start);
        assertTrue(address1Day3.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day3.get().getAmount()); // carried over

        Optional<OneTokenBalanceSeries> address2Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day3Start);
        assertTrue(address2Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Day3.get().getAmount()); // 100 - 50

        Optional<OneTokenBalanceSeries> address3Day3 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day3Start);
        assertTrue(address3Day3.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Day3.get().getAmount());
    }

    @Test
    void handle_CircularTransfers_CalculatesCorrectBalances() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");

        // Create circular transfers: address1 -> address2 -> address3 -> address1
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));
        createTokenTransfer("address2", "address3", BigInteger.valueOf(100), day1Start.plus(2, ChronoUnit.HOURS));
        createTokenTransfer("address3", "address1", BigInteger.valueOf(100), day1Start.plus(3, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // All balances should be zero due to circular nature on the next day
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address1Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Balance.isPresent());
        assertEquals(BigInteger.ZERO, address1Balance.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Balance.isPresent());
        assertEquals(BigInteger.ZERO, address2Balance.get().getAmount());

        Optional<OneTokenBalanceSeries> address3Balance = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day2Start);
        assertTrue(address3Balance.isPresent());
        assertEquals(BigInteger.ZERO, address3Balance.get().getAmount());
    }

    @Test
    void handle_SkippedDays_FillsGapsCorrectly() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day3Start = day1Start.plus(2, ChronoUnit.DAYS); // Skip day 2
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create transfers on day 1 and day 3, but not day 2
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day3Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify day 2 balances (from day 1 transfers)
        Optional<OneTokenBalanceSeries> address1Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address1", day2Start);
        assertTrue(address1Day2.isPresent());
        assertEquals(BigInteger.valueOf(-100), address1Day2.get().getAmount());

        Optional<OneTokenBalanceSeries> address2Day2 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day2Start);
        assertTrue(address2Day2.isPresent());
        assertEquals(BigInteger.valueOf(100), address2Day2.get().getAmount());

        // Day 3 has transfers, so no balances should be saved on day 3 (they get saved on day 4)

        // Verify day 4 balances (from day 3 transfers)
        Instant day4Start = day3Start.plus(1, ChronoUnit.DAYS);
        Optional<OneTokenBalanceSeries> address2Day4 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address2", day4Start);
        assertTrue(address2Day4.isPresent());
        assertEquals(BigInteger.valueOf(50), address2Day4.get().getAmount()); // 100 - 50

        Optional<OneTokenBalanceSeries> address3Day4 = oneTokenBalanceSeriesRepository.findByAddressAndTimestamp("address3", day4Start);
        assertTrue(address3Day4.isPresent());
        assertEquals(BigInteger.valueOf(50), address3Day4.get().getAmount());
    }

    @Test
    void handle_UpdateExistingLastTimestamp_UpdatesCorrectly() {
        // Given
        Instant day1Start = Instant.parse("2023-01-01T00:00:00Z");
        Instant day2Start = day1Start.plus(1, ChronoUnit.DAYS);

        // Create initial last processed timestamp
        OneTokenLastTimestamp initialTimestamp = OneTokenLastTimestamp.create(day1Start.minus(1, ChronoUnit.DAYS));
        oneTokenLastTimestampRepository.save(initialTimestamp);

        // Create transfers for day 1 and day 2
        createTokenTransfer("address1", "address2", BigInteger.valueOf(100), day1Start.plus(1, ChronoUnit.HOURS));
        createTokenTransfer("address2", "address3", BigInteger.valueOf(50), day2Start.plus(1, ChronoUnit.HOURS));

        // When
        handler.handle(command);

        // Then
        // Verify that only one timestamp record exists and it's updated to yesterday
        List<OneTokenLastTimestamp> lastTimestamps = oneTokenLastTimestampRepository.findAll();
        assertEquals(1, lastTimestamps.size());

        // With our test clock, "now" is 2023-01-06T00:00:00Z, so "yesterday" is 2023-01-05T00:00:00Z
        Instant yesterday = Instant.parse("2023-01-05T00:00:00Z");
        assertEquals(yesterday, lastTimestamps.getFirst().getLastProcessedTimestamp());
        assertEquals(initialTimestamp.getId(), lastTimestamps.getFirst().getId()); // Same entity, updated
    }
}